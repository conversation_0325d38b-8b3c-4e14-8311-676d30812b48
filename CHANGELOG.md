# Changelog

## 1.0.0 (2025-03-17)


### Features

* Add @types/qs ([7e8624f](https://github.com/harley/orchestars/commit/7e8624f826ee5df87bce78f3ebd4d59eca0b6223))
* Add About Us page ([#12](https://github.com/harley/orchestars/issues/12)) ([eae0e51](https://github.com/harley/orchestars/commit/eae0e51a52418c9b212038ce49996d91f608b614))
* Add Activities collection and display it in home page ([#24](https://github.com/harley/orchestars/issues/24)) ([6c44758](https://github.com/harley/orchestars/commit/6c4475852f617d7ce18faad5066850617c46491d))
* Add concert detail & payment pages ([111d5b8](https://github.com/harley/orchestars/commit/111d5b8031bb5c1314c963776054889116eac7dd))
* Add detailed description and configuration options to Events ([448541a](https://github.com/harley/orchestars/commit/448541a50b98524fba0ed1bcbd5c63caeeb4917a))
* Add display order to Performers & link to Partners ([#26](https://github.com/harley/orchestars/issues/26)) ([bdda49d](https://github.com/harley/orchestars/commit/bdda49d1c4c15e6a369803c0e8b7abe384b79425))
* Add event page and update dependencies ([158e7f2](https://github.com/harley/orchestars/commit/158e7f2e7846801978b82818d9b3b15409ed9ce5))
* Add footer component and improve data fetching ([#9](https://github.com/harley/orchestars/issues/9)) ([18d9a0e](https://github.com/harley/orchestars/commit/18d9a0e310a58a24c1e61edb023b60fb46eebf48))
* add initial project structure ([2aa1481](https://github.com/harley/orchestars/commit/2aa1481ca12681baf4d7f543fce67e94a984119c))
* Add password generation and handle optional fields ([#11](https://github.com/harley/orchestars/issues/11)) ([1d334a3](https://github.com/harley/orchestars/commit/1d334a30eb7f8fff54d9085cd905e4f7895590b8))
* Add TikTok icon & styling ([#20](https://github.com/harley/orchestars/issues/20)) ([3e982bd](https://github.com/harley/orchestars/commit/3e982bde62c2fbee52fc1c8f4dc81e3896a4adf3))
* Add user profile fields & event ticketing system ([db25f36](https://github.com/harley/orchestars/commit/db25f36068c0e3b78b2356f17350ded1eef096a6))
* Add Vercel Blob storage ([#8](https://github.com/harley/orchestars/issues/8)) ([db5e530](https://github.com/harley/orchestars/commit/db5e53027b3ad3250286b0f46319944b5348ed40))
* Add ZaloPay payment integration ([#1](https://github.com/harley/orchestars/issues/1)) ([21df8d4](https://github.com/harley/orchestars/commit/21df8d45649bcff5aaeb67868f1cc12120c26a18))
* **admin:** add custom logo and update admin metadata ([#48](https://github.com/harley/orchestars/issues/48)) ([54850d8](https://github.com/harley/orchestars/commit/54850d86bd62cc82719b0f1f833c72aa3cc737b4))
* **analytics:** add Vercel analytics integration ([#47](https://github.com/harley/orchestars/issues/47)) ([8933182](https://github.com/harley/orchestars/commit/89331828417e2d48e729c3fdd2269f0cf79b66a4))
* Implement transactional updates for ZaloPay callback ([#31](https://github.com/harley/orchestars/issues/31)) ([f366cda](https://github.com/harley/orchestars/commit/f366cda9fa658fdad71fe03ecb2274b1a2afd659))
* Improve event date/time picker ([#23](https://github.com/harley/orchestars/issues/23)) ([ce42851](https://github.com/harley/orchestars/commit/ce428514527b962fe09e596c0b63a42d6485709f))
* Integrate unavailable seat data into seat map ([#34](https://github.com/harley/orchestars/issues/34)) ([fa95aad](https://github.com/harley/orchestars/commit/fa95aad10c25a38d49e2c0512b18bf383e6abc5b))


### Bug Fixes

* All "payload" packages must have the same version. This is an error with your set-up, not a bug in Payload ([186683f](https://github.com/harley/orchestars/commit/186683f97dd96c660d10115e05c5e48243149ade))
* Default payment method & improve QR code component ([#43](https://github.com/harley/orchestars/issues/43)) ([4ed577a](https://github.com/harley/orchestars/commit/4ed577a3b9d443056d5460c8d6682a42d987ff2c))
* Improve payment result page logic ([0e3c6e8](https://github.com/harley/orchestars/commit/0e3c6e8777642e1b490a08fbb96f1ee5d391f478))
* Improve ZaloPay payment flow and UI ([#27](https://github.com/harley/orchestars/issues/27)) ([1aa62d4](https://github.com/harley/orchestars/commit/1aa62d49bef9c5df16117678462b251086aa7906))
* Update 404 page to use Next.js's `usePathname` ([#15](https://github.com/harley/orchestars/issues/15)) ([3335a6c](https://github.com/harley/orchestars/commit/3335a6c6810adf8c9c6540fbb0e07fa9240e8df0))
* Use req.payload.findByID in beforeChange hook ([56e5f19](https://github.com/harley/orchestars/commit/56e5f19234a247c6b48c7fcfa358f69e9250c77b))
