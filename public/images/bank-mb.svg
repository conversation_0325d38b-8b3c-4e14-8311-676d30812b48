<?xml version="1.0" encoding="UTF-8"?>
<svg width="31px" height="17px" viewBox="0 0 31 17" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 55.2 (78181) - https://sketchapp.com -->
    <title>bank-mb</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <rect id="path-1" x="0" y="0" width="36" height="36"></rect>
        <polygon id="path-3" points="30.9151677 0.133939394 30.9151677 12.5509642 15.4576117 12.5509642 5.56886228e-05 12.5509642 5.56886228e-05 0.133939394 30.9151677 0.133939394"></polygon>
        <polygon id="path-5" points="5.44163234 0.0384022039 0.12225509 0.0384022039 0.12225509 5.44052452 5.44163234 5.44052452 5.44163234 0.0384022039"></polygon>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="bank-mb" transform="translate(-3.000000, -7.000000)">
            <mask id="mask-2" fill="white">
                <use xlink:href="#path-1"></use>
            </mask>
            <g id="Mask"></g>
            <g id="mb" mask="url(#mask-2)">
                <g transform="translate(3.000000, 7.000000)" id="Page-1">
                    <g>
                        <g id="Group-3" transform="translate(0.000000, 3.895557)">
                            <mask id="mask-4" fill="white">
                                <use xlink:href="#path-3"></use>
                            </mask>
                            <g id="Clip-2"></g>
                            <path d="M29.1640018,6.68928926 C30.0320018,6.2741708 30.9152234,5.48402204 30.9152234,3.69897521 C30.9152234,2.87154821 30.6245287,2.11455647 30.1192473,1.52615978 C29.3316246,0.601134986 27.9971395,0.133939394 26.2652234,0.133939394 L19.0492772,0.133939394 L17.2403251,2.41390634 L26.6526305,2.41390634 C27.340385,2.41390634 27.5468042,2.61116253 27.7112713,2.86349311 C27.9260437,3.18269972 27.901912,3.61449036 27.8178222,4.04609366 C27.6357204,4.96456198 27.1434329,5.29838017 26.4638461,5.29838017 L25.3665946,5.29838017 C25.4358341,4.96774656 25.7603132,3.62235813 25.7603132,3.62235813 L22.2402353,3.62235813 L20.6907922,10.2773664 L20.1645347,10.2773664 C19.6254689,10.2773664 19.531912,9.97764187 19.6286246,9.57376309 C20.1500557,7.38465014 21.0234389,3.61449036 21.0234389,3.61449036 L16.2878641,3.61449036 C16.2878641,3.61449036 14.1271455,6.32137741 13.4507144,7.16191736 C13.7202473,6.00835262 15.0935287,0.133939394 15.0935287,0.133939394 L11.310415,0.133939394 C10.7503731,1.87496419 9.18032515,5.18130028 7.9503491,6.68928926 C6.97078623,7.89455647 5.89896587,8.6892011 4.79837305,9.03819284 C2.75998383,9.67960331 0.845965868,8.37355372 0.845965868,8.37355372 L5.56886228e-05,12.0346887 L0.1519,12.0706556 C1.95936707,12.5968595 3.64283413,12.6897741 5.15032515,12.3477135 C6.36229521,12.0672837 7.46307365,11.5152287 8.41831916,10.6893003 C9.18032515,10.0284077 9.72236108,9.31656198 10.0936186,8.72872727 C9.75466048,10.1258182 9.17716946,12.5509642 9.17716946,12.5509642 L10.8088461,12.5509642 C12.7534928,12.5509642 13.3651395,12.0152066 14.751415,10.2773664 C14.751415,10.2773664 16.1651635,8.48595041 16.4447204,8.14220386 C16.3476365,8.55769697 16.2074868,9.19273829 15.9988401,10.0900386 C15.6247982,11.7432066 16.5332653,12.5509642 18.2778042,12.5509642 L23.6831275,12.5509642 C23.6831275,12.5509642 24.7740677,7.82280992 24.8321695,7.58696419 L26.3654629,7.58696419 C27.6451874,7.58696419 27.4532473,8.71692562 27.3660018,9.12099174 C27.1755467,10.0900386 26.5074689,10.2741818 25.8811575,10.2741818 L24.6740138,10.2741818 L25.1971156,12.5509642 L27.8178222,12.5509642 C28.6605766,12.5509642 30.9037144,12.1032507 30.9037144,9.36695317 C30.9037144,8.09181267 30.0936305,7.18102479 29.1640018,6.68928926" id="Fill-1" fill="#1478BE" mask="url(#mask-4)"></path>
                        </g>
                        <g id="Group-6" transform="translate(3.526946, 0.149001)">
                            <mask id="mask-6" fill="white">
                                <use xlink:href="#path-5"></use>
                            </mask>
                            <g id="Clip-5"></g>
                            <polygon id="Fill-4" fill="#EE393A" mask="url(#mask-6)" points="3.86657246 3.06127383 5.44163234 1.86724628 3.36481796 1.86724628 2.60615329 0.0383647383 2.17363832 1.86724628 0.12225509 1.86724628 1.87032096 3.18509752 1.4766024 4.88846942 2.79011138 3.8804584 4.84650659 5.44052452"></polygon>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>