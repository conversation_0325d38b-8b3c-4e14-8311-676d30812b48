name: "Lint PR Title"

on:
  pull_request_target:
    types:
      - opened
      - edited
      - synchronize

permissions:
  pull-requests: read
  statuses: write

jobs:
  main:
    name: Validate PR Title
    runs-on: ubuntu-latest
    steps:
      - uses: amannn/action-semantic-pull-request@v5
        id: lint_pr_title
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          types: |
            feat
            fix
            docs
            style
            refactor
            perf
            test
            build
            ci
            chore
            revert
          scopes: |
            auth
            db
            ui
            api
            deps
            core
            test
          wip: true
          headerPattern: '^([a-zA-Z]*)(?:\(([\w$.\-*/ ]*)\))?: (.*)$'
          headerPatternCorrespondence: type, scope, subject

      - uses: marocchino/sticky-pull-request-comment@v2
        if: always() && (steps.lint_pr_title.outputs.error_message != null)
        with:
          header: pr-title-lint-error
          message: |
            Hey there and thank you for opening this pull request! 👋

            We require pull request titles to follow the [Conventional Commits specification](https://www.conventionalcommits.org/en/v1.0.0/) and it looks like your proposed title needs to be adjusted.

            Valid format: `type(scope): subject`
            - type: feat, fix, docs, style, refactor, perf, test, build, ci, chore, revert
            - scope: auth, db, ui, api, deps, core, test (optional)
            - subject: can start with any case

            Examples:
            ✅ feat(auth): Add OAuth2 support
            ✅ fix(db): Resolve connection timeout
            ✅ docs: Update README
            ✅ feat: Add new feature
            ❌ random text without type
            ❌ : missing type

            Details:
            ```
            ${{ steps.lint_pr_title.outputs.error_message }}
            ```

      - if: ${{ steps.lint_pr_title.outputs.error_message == null }}
        uses: marocchino/sticky-pull-request-comment@v2
        with:
          header: pr-title-lint-error
          delete: true 