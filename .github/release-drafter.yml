name-template: 'v$RESOLVED_VERSION'
tag-template: 'v$RESOLVED_VERSION'
categories:
  - title: '🚀 Features'
    labels:
      - 'feat'
  - title: '🐛 Bug Fixes'
    labels:
      - 'fix'
  - title: '📚 Documentation'
    labels:
      - 'docs'
  - title: '🧰 Maintenance'
    labels:
      - 'chore'
      - 'refactor'
      - 'perf'
      - 'test'
      - 'ci'
      - 'build'

change-template: '- $TITLE (#$NUMBER)'

version-resolver:
  major:
    labels:
      - 'breaking'
  minor:
    labels:
      - 'feat'
  patch:
    labels:
      - 'fix'
      - 'docs'
      - 'chore'
      - 'refactor'
      - 'test'
      - 'ci'
      - 'build'
  default: patch

template: |
  ## What's Changed
  $CHANGES

  **Full Changelog**: https://github.com/$OWNER/$REPOSITORY/compare/$PREVIOUS_TAG...v$RESOLVED_VERSION

autolabeler:
  - label: 'feat'
    title:
      - '/^feat(\([^)]*\))?:/i'
  - label: 'fix'
    title:
      - '/^fix(\([^)]*\))?:/i'
  - label: 'chore'
    title:
      - '/^chore(\([^)]*\))?:/i'
  - label: 'docs'
    title:
      - '/^docs(\([^)]*\))?:/i'
  - label: 'breaking'
    title:
      - '/!:/' # Matches conventional commit breaking change indicator