# Database connection string
# For standard PostgreSQL
DATABASE_URI=postgres://postgres:<password>@127.0.0.1:5432/your-database-name

# For Supabase local development
# DATABASE_URI=postgres://postgres:postgres@127.0.0.1:54322/postgres

# Or use a PG connection string
#DATABASE_URI=postgresql://127.0.0.1:5432/your-database-name

# Used to encrypt JWT tokens
PAYLOAD_SECRET=YOUR_SECRET_HERE

# Used to configure CORS, format links and more. No trailing slash
NEXT_PUBLIC_SERVER_URL=http://localhost:3000

# Secret used to authenticate cron jobs
CRON_SECRET=YOUR_CRON_SECRET_HERE

# Used to validate preview requests
PREVIEW_SECRET=YOUR_SECRET_HERE

ZALO_APP_ID=
ZALO_API_URL=https://sb-openapi.zalopay.vn
ZALO_KEY1=
ZALO_KEY2=
ZALO_GET_BANK_ENDPOINT=
APP_BASE_URL=http://localhost:3000
ZALO_REDIRECT_URL=http://localhost:3000/payment/result
ZALO_CALLBACK_URL=http://localhost:3000/api/zalopay/callback

# Storage Configuration
# You can configure both S3 and Vercel Blob storage simultaneously
# S3 will be used as the primary storage if configured

# S3 Storage (Supabase recommended)
S3_ACCESS_KEY=supabase
S3_SECRET_KEY=your-supabase-anon-key
S3_BUCKET=your-bucket-name
S3_REGION=us-east-1
S3_ENDPOINT=https://your-project-id.supabase.co/storage/v1
S3_ACL=public-read

# Alternative: AWS S3 Storage
# S3_ACCESS_KEY=your-aws-access-key
# S3_SECRET_KEY=your-aws-secret-key
# S3_BUCKET=your-bucket-name
# S3_REGION=your-region
# S3_ENDPOINT=  # Optional: For S3-compatible services
# S3_ACL=public-read  # Optional: Default is 'public-read'

# Vercel Blob Storage (used as fallback if S3 is not configured)
BLOB_READ_WRITE_TOKEN="vercel_blob_rw_xyz"
BLOB_BASE_URL=https://test.orchestars.vn

# Email Configuration
# Email Provider: 'RESEND' or 'NODEMAILER'
EMAIL_PROVIDER=RESEND
EMAIL_DEFAULT_FROM_ADDRESS=<EMAIL>
EMAIL_DEFAULT_FROM_NAME=Orchestars
EMAIL_CC=
# Email Admins
EMAIL_ADMIN_CC="<EMAIL>,<EMAIL>"

# Resend (for production)
RESEND_API_KEY=re_apikey

# Nodemailer (for local development with Inbucket)
SMTP_HOST=localhost
SMTP_PORT=2500
SMTP_USER=
SMTP_PASS=

# PAYMENT
#VIETQR
# refer: https://www.vietqr.io/danh-sach-api/link-tao-ma-nhanh/api-tao-ma-qr/
VIET_QR_X_CLIENT_ID=
VIET_QR_X_API_KEY=
VIET_QR_ACCOUNT_NO=
VIET_QR_ACCOUNT_NAME=""
VIET_QR_BANK_NAME="Ngân hàng thương mại cổ phần Ngoại thương Việt Nam"
VIET_QR_ACQ_ID=[Bin Card] # https://developers.momo.vn/v3/docs/payment/api/result-handling/bankcode/
VIET_QR_TEMPLATE=compact
VIET_QR_ENCRYPT_KEY=

# DEFAULT LOCALE WEB
NEXT_PUBLIC_DEFAULT_FALLBACK_LOCALE='vi'

# JWT
JWT_USER_SECRET=YOUR_SECRET_HERE
JWT_USER_EXPIRATION=3600000

# INTERNAL API KEY
X_API_KEY=xApiKey

# GOOGLE TAG MANAGER
NEXT_PUBLIC_GOOGLE_TAG_MANAGER_ID=GTM-XXXXXX
