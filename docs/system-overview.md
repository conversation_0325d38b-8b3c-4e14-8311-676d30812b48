# Orchestars System Overview

## Technology Stack

- **Framework**: Next.js with Payload CMS
- **Database**: PostgreSQL on Supabase (with local development support)
- **Deployment**: Vercel
- **CMS**: Payload CMS (headless)
- **File Storage**: Supabase Storage / AWS S3 / Vercel Blob Storage / Local filesystem
- **Authentication**: Payload built-in + Phone verification
- **APIs**: REST + GraphQL (auto-generated by Payload)

## Core System Components

### 1. Content Management (Payload CMS)

Payload CMS serves as our headless CMS and application framework, providing:
- Admin UI for content management
- Database schema management
- REST and GraphQL APIs
- Authentication system with dedicated Admins collection
- Access control
- File storage and management

Key Benefits:
- Type-safe development with auto-generated TypeScript types
- Built-in versioning and drafts
- Extensible admin UI with custom components
- Flexible access control patterns
- HTTP-only cookie based authentication

### 2. Admin Interface

The admin interface is built using Payload's custom component system, allowing for:
- Custom pages and views (e.g., `/admin/event/[eventId]` for ticket/seat management)
- Extended functionality beyond default CRUD operations
- Seamless integration with Payload's authentication
- Consistent design using Payload's CSS library
- Server and Client component support

Admin users are managed through a dedicated `Admins` collection with:
- Email/password authentication
- Role-based access (admin, super-admin)
- Activity tracking (lastActive)
- Secure session management
- Profile fields (firstName, lastName)

### 2. Event Management System

#### Event Structure
- Events can be concerts, theater shows, or performances
- Each event has:
  - Basic info (title, description, location)
  - Multiple schedules with dates and times
  - Media assets (logo, banner, thumbnail)
  - Terms and conditions
  - Ticket pricing configuration
  - Status workflow (draft → published_upcoming → published_open_sales → completed/cancelled)
  - SEO metadata
  - Performer/artist information

#### Ticketing System
- **Zone-Based Sales**:
  - Dynamic zones per venue (up to 5 zones)
  - Zones are price-tiered (Zone 1 highest price)
  - No pre-selected seats during purchase
  - Post-purchase seat allocation for fairness
  - Configurable per venue/event

- **Pricing & Inventory**:
  - Each zone has:
    - Base price
    - Total quantity
    - Current inventory
    - Currency specification
  - Early bird pricing through promotion system
  - Support for multiple currencies
  - Real-time inventory tracking

- **Purchase Flow**:
  1. Customer selects zone and quantity
  2. System creates 20-minute hold
  3. Customer completes payment
  4. System allocates seats within purchased zone
  5. Tickets are generated and issued
  6. Confirmation sent to customer

### 3. Database Schema

Key collections in Payload CMS:
```typescript
collections: {
  // Admin & Authentication
  admins: Admin                   // Admin users with authentication

  // Event Management
  events: Event                    // Event details and configuration
  performers: Performer           // Artists and performers
  partners: Partner              // Event partners and sponsors

  // Ticketing
  orders: Order                   // Customer orders
  orderItems: OrderItem          // Individual items in orders
  payments: Payment              // Payment transactions
  tickets: Ticket                // Generated tickets
  seatHoldings: SeatHolding     // Temporary seat holds

  // Promotions
  promotions: Promotion          // Discount configurations
  userPromotionRedemptions: UserPromotionRedemption  // Promotion usage

  // Users & Content
  users: User                    // User accounts
  media: Media                   // Uploaded files
  pages: Page                    // Static pages
  posts: Post                    // Blog/news posts

  // Support
  faqs: FAQ                      // Frequently asked questions
  activities: Activity           // System activities
}
```

### 4. Integration Points

- **Payment Processing**:
  - Integration with ZaloPay payment gateway
  - Implementation follows ZaloPay v2 API documentation
  - Payment flow:
    1. Create payment order
    2. Redirect to ZaloPay gateway
    3. Handle payment callback
  - Transaction data stored in payments collection

- **Communication**:
  - Email notifications for primary communication
  - Phone numbers stored for customer support only
  - No active phone verification required
  - Email services:
    - Production: Resend (via `@payloadcms/email-resend`)
    - Development: Nodemailer with Inbucket (via `@payloadcms/email-nodemailer`)
  - Email templates for:
    - Ticket confirmation
    - Order status updates
    - Event reminders
    - Check-in instructions

### 5. Business Rules

#### Event Status Management
```typescript
type EventStatus = 'draft' | 'published_upcoming' | 'published_open_sales' | 'completed' | 'cancelled'
```
- Status changes managed manually by admin
- No automatic status transitions
- Supports full event lifecycle management

#### Seat Allocation System
- Flexible seat assignment model:
  - Seats are optional in the schema
  - Similar to airline boarding model
  - Seats can be assigned/updated post-purchase
- Zone Capacity Management:
  - Strict enforcement of zone capacity limits
  - Validation against ticket pricing tiers
  - Real-time inventory tracking per zone

#### Ticket Zone Configuration
```typescript
ticketPrices: {
  name: string
  key: 'zone1' | 'zone2' | 'zone3' | 'zone4' | 'zone5'  // Price tiers
  price: number
  currency: string
  quantity: number
}[]
```

## Key Features

### 1. Temporary Hold System
- 20-minute hold duration
- Prevents double-booking
- Tracks user context (IP, user agent)
- Automatic release on expiration
- Configurable timeout periods
- Hold limit per user

### 2. Promotion System
- Supports early bird pricing
- Flexible discount types (percentage/fixed)
- Per-user redemption limits
- Zone-specific applicability
- Time-based validity
- Usage tracking
- Stackable/non-stackable options

### 3. User Management
- Role-based access (admin, super-admin, customer)
- Phone number verification
- Order history tracking
- Promotion redemption tracking
- Profile management
- Security logging

## Caching Strategy

- Homepage carousel of events uses Next.js caching
- Event pages are cached but updatable
- Real-time inventory checks bypass cache
- Cached components:
  - Event listings
  - Static content
  - Media assets
- Cache invalidation on:
  - Content updates
  - Inventory changes
  - Price changes

## Security Measures

- **Authentication**:
  - Payload's built-in authentication for admin users
  - HTTP-only cookie based session management
  - Phone verification for customers
  - Rate limiting on auth endpoints
  - Role-based access control (admin vs super-admin)
  - Secure password hashing and salting
  - Session timeout configuration

- **Data Protection**:
  - Input validation
  - SQL injection prevention
  - XSS protection
  - CSRF tokens

- **Transaction Security**:
  - Atomic operations for critical transactions
  - Audit logging
  - Payment data encryption

## Development Guidelines

### API Access
- REST and GraphQL endpoints available
- Authentication using Payload's built-in system
- Role-based access control for admin endpoints
- Rate limiting applied [To be implemented]
- API versioning strategy
- Error handling standards

### Database Access
- All database operations through Payload CMS
- Direct Postgres access for reporting only
- Migrations handled by Payload
- Backup strategy
- Data retention policies

### Supabase Integration
- Local development using Supabase CLI
- PostgreSQL database for development and testing
- Inbucket for email testing
- Database management through Supabase Studio
- Configuration in `supabase/config.toml`

### Storage Integration
- Multiple storage providers can be used simultaneously:
  - S3 Storage (including Supabase) - Primary storage if configured
  - Vercel Blob Storage - Included as fallback
  - Local filesystem - Default if no other providers are configured
- Flexible configuration through environment variables
- Media file management
- Export file storage
- Implemented in `src/plugins/storage.ts`
- Seamless integration with Supabase local development environment
- Automatic fallback between storage providers

### File Structure
```
src/
  ├── app/                 # Next.js app directory
  │   ├── (frontend)/     # Public-facing routes
  │   ├── (payload)/      # Admin routes
  │   └── api/            # API routes
  ├── collections/        # Payload collections
  │   ├── Events/
  │   ├── Orders/
  │   └── Users/
  ├── components/        # React components
  │   ├── common/
  │   └── modules/
  ├── hooks/            # Custom hooks
  ├── utilities/        # Helper functions
  └── payload.config.ts  # Payload configuration
```

## Current Status

### Implemented Features
- Basic event management
- Ticketing system operational
- Zone-based sales
- Temporary hold system
- User authentication
- Admin interface

### Pending Implementation
- Payment integration
- Email notifications
- External sales integration
- Advanced reporting
- Marketing integrations

### Known Limitations
- Seat selection disabled
- Limited reporting capabilities
- Basic promotion system

## Monitoring & Maintenance

- Error tracking
- Performance monitoring
- Database health checks
- Backup procedures
- Update strategy

## Future Roadmap

See `tech-FAQs.md` for detailed questions about:
- Early bird implementation details
- Seat allocation algorithms
- Inventory management
- Multi-platform integration
- Security measures
- Performance optimization