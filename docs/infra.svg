<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 12.1.1 (20240910.0053)
 -->
<!-- Title: INFRA Pages: 1 -->
<svg width="4000pt" height="80pt"
 viewBox="0.00 0.00 3999.77 80.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 76)">
<title>INFRA</title>
<polygon fill="white" stroke="none" points="-4,4 -4,-76 3995.77,-76 3995.77,4 -4,4"/>
<!-- Pages -->
<g id="node1" class="node">
<title>Pages</title>
<path fill="#fff5cd" stroke="black" stroke-width="2" d="M145.17,-65.45C145.17,-69.07 112.64,-72 72.59,-72 32.53,-72 0,-69.07 0,-65.45 0,-65.45 0,-6.55 0,-6.55 0,-2.93 32.53,0 72.59,0 112.64,0 145.17,-2.93 145.17,-6.55 145.17,-6.55 145.17,-65.45 145.17,-65.45"/>
<path fill="none" stroke="black" stroke-width="2" d="M145.17,-65.45C145.17,-61.84 112.64,-58.91 72.59,-58.91 32.53,-58.91 0,-61.84 0,-65.45"/>
<text text-anchor="middle" x="72.59" y="-40.2" font-family="Arial" font-size="14.00">Pages</text>
<text text-anchor="middle" x="72.59" y="-23.4" font-family="Arial" font-size="14.00">(Payload::Collection)</text>
</g>
<!-- Posts -->
<g id="node2" class="node">
<title>Posts</title>
<path fill="#fff5cd" stroke="black" stroke-width="2" d="M308.17,-65.45C308.17,-69.07 275.64,-72 235.59,-72 195.53,-72 163,-69.07 163,-65.45 163,-65.45 163,-6.55 163,-6.55 163,-2.93 195.53,0 235.59,0 275.64,0 308.17,-2.93 308.17,-6.55 308.17,-6.55 308.17,-65.45 308.17,-65.45"/>
<path fill="none" stroke="black" stroke-width="2" d="M308.17,-65.45C308.17,-61.84 275.64,-58.91 235.59,-58.91 195.53,-58.91 163,-61.84 163,-65.45"/>
<text text-anchor="middle" x="235.59" y="-40.2" font-family="Arial" font-size="14.00">Posts</text>
<text text-anchor="middle" x="235.59" y="-23.4" font-family="Arial" font-size="14.00">(Payload::Collection)</text>
</g>
<!-- Media -->
<g id="node3" class="node">
<title>Media</title>
<path fill="#fff5cd" stroke="black" stroke-width="2" d="M471.17,-65.45C471.17,-69.07 438.64,-72 398.59,-72 358.53,-72 326,-69.07 326,-65.45 326,-65.45 326,-6.55 326,-6.55 326,-2.93 358.53,0 398.59,0 438.64,0 471.17,-2.93 471.17,-6.55 471.17,-6.55 471.17,-65.45 471.17,-65.45"/>
<path fill="none" stroke="black" stroke-width="2" d="M471.17,-65.45C471.17,-61.84 438.64,-58.91 398.59,-58.91 358.53,-58.91 326,-61.84 326,-65.45"/>
<text text-anchor="middle" x="398.59" y="-40.2" font-family="Arial" font-size="14.00">Media</text>
<text text-anchor="middle" x="398.59" y="-23.4" font-family="Arial" font-size="14.00">(Payload::Collection)</text>
</g>
<!-- Categories -->
<g id="node4" class="node">
<title>Categories</title>
<path fill="#fff5cd" stroke="black" stroke-width="2" d="M634.17,-65.45C634.17,-69.07 601.64,-72 561.59,-72 521.53,-72 489,-69.07 489,-65.45 489,-65.45 489,-6.55 489,-6.55 489,-2.93 521.53,0 561.59,0 601.64,0 634.17,-2.93 634.17,-6.55 634.17,-6.55 634.17,-65.45 634.17,-65.45"/>
<path fill="none" stroke="black" stroke-width="2" d="M634.17,-65.45C634.17,-61.84 601.64,-58.91 561.59,-58.91 521.53,-58.91 489,-61.84 489,-65.45"/>
<text text-anchor="middle" x="561.59" y="-40.2" font-family="Arial" font-size="14.00">Categories</text>
<text text-anchor="middle" x="561.59" y="-23.4" font-family="Arial" font-size="14.00">(Payload::Collection)</text>
</g>
<!-- Users -->
<g id="node5" class="node">
<title>Users</title>
<path fill="#fff5cd" stroke="black" stroke-width="2" d="M797.17,-65.45C797.17,-69.07 764.64,-72 724.59,-72 684.53,-72 652,-69.07 652,-65.45 652,-65.45 652,-6.55 652,-6.55 652,-2.93 684.53,0 724.59,0 764.64,0 797.17,-2.93 797.17,-6.55 797.17,-6.55 797.17,-65.45 797.17,-65.45"/>
<path fill="none" stroke="black" stroke-width="2" d="M797.17,-65.45C797.17,-61.84 764.64,-58.91 724.59,-58.91 684.53,-58.91 652,-61.84 652,-65.45"/>
<text text-anchor="middle" x="724.59" y="-40.2" font-family="Arial" font-size="14.00">Users</text>
<text text-anchor="middle" x="724.59" y="-23.4" font-family="Arial" font-size="14.00">(Payload::Collection)</text>
</g>
<!-- CheckInRecords -->
<g id="node6" class="node">
<title>CheckInRecords</title>
<path fill="#fff5cd" stroke="black" stroke-width="2" d="M960.17,-65.45C960.17,-69.07 927.64,-72 887.59,-72 847.53,-72 815,-69.07 815,-65.45 815,-65.45 815,-6.55 815,-6.55 815,-2.93 847.53,0 887.59,0 927.64,0 960.17,-2.93 960.17,-6.55 960.17,-6.55 960.17,-65.45 960.17,-65.45"/>
<path fill="none" stroke="black" stroke-width="2" d="M960.17,-65.45C960.17,-61.84 927.64,-58.91 887.59,-58.91 847.53,-58.91 815,-61.84 815,-65.45"/>
<text text-anchor="middle" x="887.59" y="-40.2" font-family="Arial" font-size="14.00">CheckInRecords</text>
<text text-anchor="middle" x="887.59" y="-23.4" font-family="Arial" font-size="14.00">(Payload::Collection)</text>
</g>
<!-- Events -->
<g id="node7" class="node">
<title>Events</title>
<path fill="#fff5cd" stroke="black" stroke-width="2" d="M1123.17,-65.45C1123.17,-69.07 1090.64,-72 1050.59,-72 1010.53,-72 978,-69.07 978,-65.45 978,-65.45 978,-6.55 978,-6.55 978,-2.93 1010.53,0 1050.59,0 1090.64,0 1123.17,-2.93 1123.17,-6.55 1123.17,-6.55 1123.17,-65.45 1123.17,-65.45"/>
<path fill="none" stroke="black" stroke-width="2" d="M1123.17,-65.45C1123.17,-61.84 1090.64,-58.91 1050.59,-58.91 1010.53,-58.91 978,-61.84 978,-65.45"/>
<text text-anchor="middle" x="1050.59" y="-40.2" font-family="Arial" font-size="14.00">Events</text>
<text text-anchor="middle" x="1050.59" y="-23.4" font-family="Arial" font-size="14.00">(Payload::Collection)</text>
</g>
<!-- Promotions -->
<g id="node8" class="node">
<title>Promotions</title>
<path fill="#fff5cd" stroke="black" stroke-width="2" d="M1286.17,-65.45C1286.17,-69.07 1253.64,-72 1213.59,-72 1173.53,-72 1141,-69.07 1141,-65.45 1141,-65.45 1141,-6.55 1141,-6.55 1141,-2.93 1173.53,0 1213.59,0 1253.64,0 1286.17,-2.93 1286.17,-6.55 1286.17,-6.55 1286.17,-65.45 1286.17,-65.45"/>
<path fill="none" stroke="black" stroke-width="2" d="M1286.17,-65.45C1286.17,-61.84 1253.64,-58.91 1213.59,-58.91 1173.53,-58.91 1141,-61.84 1141,-65.45"/>
<text text-anchor="middle" x="1213.59" y="-40.2" font-family="Arial" font-size="14.00">Promotions</text>
<text text-anchor="middle" x="1213.59" y="-23.4" font-family="Arial" font-size="14.00">(Payload::Collection)</text>
</g>
<!-- UserPromotionRedemptions -->
<g id="node9" class="node">
<title>UserPromotionRedemptions</title>
<path fill="#fff5cd" stroke="black" stroke-width="2" d="M1496.51,-65.45C1496.51,-69.07 1453.52,-72 1400.59,-72 1347.65,-72 1304.66,-69.07 1304.66,-65.45 1304.66,-65.45 1304.66,-6.55 1304.66,-6.55 1304.66,-2.93 1347.65,0 1400.59,0 1453.52,0 1496.51,-2.93 1496.51,-6.55 1496.51,-6.55 1496.51,-65.45 1496.51,-65.45"/>
<path fill="none" stroke="black" stroke-width="2" d="M1496.51,-65.45C1496.51,-61.84 1453.52,-58.91 1400.59,-58.91 1347.65,-58.91 1304.66,-61.84 1304.66,-65.45"/>
<text text-anchor="middle" x="1400.59" y="-40.2" font-family="Arial" font-size="14.00">UserPromotionRedemptions</text>
<text text-anchor="middle" x="1400.59" y="-23.4" font-family="Arial" font-size="14.00">(Payload::Collection)</text>
</g>
<!-- Orders -->
<g id="node10" class="node">
<title>Orders</title>
<path fill="#fff5cd" stroke="black" stroke-width="2" d="M1660.17,-65.45C1660.17,-69.07 1627.64,-72 1587.59,-72 1547.53,-72 1515,-69.07 1515,-65.45 1515,-65.45 1515,-6.55 1515,-6.55 1515,-2.93 1547.53,0 1587.59,0 1627.64,0 1660.17,-2.93 1660.17,-6.55 1660.17,-6.55 1660.17,-65.45 1660.17,-65.45"/>
<path fill="none" stroke="black" stroke-width="2" d="M1660.17,-65.45C1660.17,-61.84 1627.64,-58.91 1587.59,-58.91 1547.53,-58.91 1515,-61.84 1515,-65.45"/>
<text text-anchor="middle" x="1587.59" y="-40.2" font-family="Arial" font-size="14.00">Orders</text>
<text text-anchor="middle" x="1587.59" y="-23.4" font-family="Arial" font-size="14.00">(Payload::Collection)</text>
</g>
<!-- OrderItems -->
<g id="node11" class="node">
<title>OrderItems</title>
<path fill="#fff5cd" stroke="black" stroke-width="2" d="M1823.17,-65.45C1823.17,-69.07 1790.64,-72 1750.59,-72 1710.53,-72 1678,-69.07 1678,-65.45 1678,-65.45 1678,-6.55 1678,-6.55 1678,-2.93 1710.53,0 1750.59,0 1790.64,0 1823.17,-2.93 1823.17,-6.55 1823.17,-6.55 1823.17,-65.45 1823.17,-65.45"/>
<path fill="none" stroke="black" stroke-width="2" d="M1823.17,-65.45C1823.17,-61.84 1790.64,-58.91 1750.59,-58.91 1710.53,-58.91 1678,-61.84 1678,-65.45"/>
<text text-anchor="middle" x="1750.59" y="-40.2" font-family="Arial" font-size="14.00">OrderItems</text>
<text text-anchor="middle" x="1750.59" y="-23.4" font-family="Arial" font-size="14.00">(Payload::Collection)</text>
</g>
<!-- Payments -->
<g id="node12" class="node">
<title>Payments</title>
<path fill="#fff5cd" stroke="black" stroke-width="2" d="M1986.17,-65.45C1986.17,-69.07 1953.64,-72 1913.59,-72 1873.53,-72 1841,-69.07 1841,-65.45 1841,-65.45 1841,-6.55 1841,-6.55 1841,-2.93 1873.53,0 1913.59,0 1953.64,0 1986.17,-2.93 1986.17,-6.55 1986.17,-6.55 1986.17,-65.45 1986.17,-65.45"/>
<path fill="none" stroke="black" stroke-width="2" d="M1986.17,-65.45C1986.17,-61.84 1953.64,-58.91 1913.59,-58.91 1873.53,-58.91 1841,-61.84 1841,-65.45"/>
<text text-anchor="middle" x="1913.59" y="-40.2" font-family="Arial" font-size="14.00">Payments</text>
<text text-anchor="middle" x="1913.59" y="-23.4" font-family="Arial" font-size="14.00">(Payload::Collection)</text>
</g>
<!-- Tickets -->
<g id="node13" class="node">
<title>Tickets</title>
<path fill="#fff5cd" stroke="black" stroke-width="2" d="M2149.17,-65.45C2149.17,-69.07 2116.64,-72 2076.59,-72 2036.53,-72 2004,-69.07 2004,-65.45 2004,-65.45 2004,-6.55 2004,-6.55 2004,-2.93 2036.53,0 2076.59,0 2116.64,0 2149.17,-2.93 2149.17,-6.55 2149.17,-6.55 2149.17,-65.45 2149.17,-65.45"/>
<path fill="none" stroke="black" stroke-width="2" d="M2149.17,-65.45C2149.17,-61.84 2116.64,-58.91 2076.59,-58.91 2036.53,-58.91 2004,-61.84 2004,-65.45"/>
<text text-anchor="middle" x="2076.59" y="-40.2" font-family="Arial" font-size="14.00">Tickets</text>
<text text-anchor="middle" x="2076.59" y="-23.4" font-family="Arial" font-size="14.00">(Payload::Collection)</text>
</g>
<!-- SeatHoldings -->
<g id="node14" class="node">
<title>SeatHoldings</title>
<path fill="#fff5cd" stroke="black" stroke-width="2" d="M2312.17,-65.45C2312.17,-69.07 2279.64,-72 2239.59,-72 2199.53,-72 2167,-69.07 2167,-65.45 2167,-65.45 2167,-6.55 2167,-6.55 2167,-2.93 2199.53,0 2239.59,0 2279.64,0 2312.17,-2.93 2312.17,-6.55 2312.17,-6.55 2312.17,-65.45 2312.17,-65.45"/>
<path fill="none" stroke="black" stroke-width="2" d="M2312.17,-65.45C2312.17,-61.84 2279.64,-58.91 2239.59,-58.91 2199.53,-58.91 2167,-61.84 2167,-65.45"/>
<text text-anchor="middle" x="2239.59" y="-40.2" font-family="Arial" font-size="14.00">SeatHoldings</text>
<text text-anchor="middle" x="2239.59" y="-23.4" font-family="Arial" font-size="14.00">(Payload::Collection)</text>
</g>
<!-- Partners -->
<g id="node15" class="node">
<title>Partners</title>
<path fill="#fff5cd" stroke="black" stroke-width="2" d="M2475.17,-65.45C2475.17,-69.07 2442.64,-72 2402.59,-72 2362.53,-72 2330,-69.07 2330,-65.45 2330,-65.45 2330,-6.55 2330,-6.55 2330,-2.93 2362.53,0 2402.59,0 2442.64,0 2475.17,-2.93 2475.17,-6.55 2475.17,-6.55 2475.17,-65.45 2475.17,-65.45"/>
<path fill="none" stroke="black" stroke-width="2" d="M2475.17,-65.45C2475.17,-61.84 2442.64,-58.91 2402.59,-58.91 2362.53,-58.91 2330,-61.84 2330,-65.45"/>
<text text-anchor="middle" x="2402.59" y="-40.2" font-family="Arial" font-size="14.00">Partners</text>
<text text-anchor="middle" x="2402.59" y="-23.4" font-family="Arial" font-size="14.00">(Payload::Collection)</text>
</g>
<!-- Performers -->
<g id="node16" class="node">
<title>Performers</title>
<path fill="#fff5cd" stroke="black" stroke-width="2" d="M2638.17,-65.45C2638.17,-69.07 2605.64,-72 2565.59,-72 2525.53,-72 2493,-69.07 2493,-65.45 2493,-65.45 2493,-6.55 2493,-6.55 2493,-2.93 2525.53,0 2565.59,0 2605.64,0 2638.17,-2.93 2638.17,-6.55 2638.17,-6.55 2638.17,-65.45 2638.17,-65.45"/>
<path fill="none" stroke="black" stroke-width="2" d="M2638.17,-65.45C2638.17,-61.84 2605.64,-58.91 2565.59,-58.91 2525.53,-58.91 2493,-61.84 2493,-65.45"/>
<text text-anchor="middle" x="2565.59" y="-40.2" font-family="Arial" font-size="14.00">Performers</text>
<text text-anchor="middle" x="2565.59" y="-23.4" font-family="Arial" font-size="14.00">(Payload::Collection)</text>
</g>
<!-- Activities -->
<g id="node17" class="node">
<title>Activities</title>
<path fill="#fff5cd" stroke="black" stroke-width="2" d="M2801.17,-65.45C2801.17,-69.07 2768.64,-72 2728.59,-72 2688.53,-72 2656,-69.07 2656,-65.45 2656,-65.45 2656,-6.55 2656,-6.55 2656,-2.93 2688.53,0 2728.59,0 2768.64,0 2801.17,-2.93 2801.17,-6.55 2801.17,-6.55 2801.17,-65.45 2801.17,-65.45"/>
<path fill="none" stroke="black" stroke-width="2" d="M2801.17,-65.45C2801.17,-61.84 2768.64,-58.91 2728.59,-58.91 2688.53,-58.91 2656,-61.84 2656,-65.45"/>
<text text-anchor="middle" x="2728.59" y="-40.2" font-family="Arial" font-size="14.00">Activities</text>
<text text-anchor="middle" x="2728.59" y="-23.4" font-family="Arial" font-size="14.00">(Payload::Collection)</text>
</g>
<!-- FAQs -->
<g id="node18" class="node">
<title>FAQs</title>
<path fill="#fff5cd" stroke="black" stroke-width="2" d="M2964.17,-65.45C2964.17,-69.07 2931.64,-72 2891.59,-72 2851.53,-72 2819,-69.07 2819,-65.45 2819,-65.45 2819,-6.55 2819,-6.55 2819,-2.93 2851.53,0 2891.59,0 2931.64,0 2964.17,-2.93 2964.17,-6.55 2964.17,-6.55 2964.17,-65.45 2964.17,-65.45"/>
<path fill="none" stroke="black" stroke-width="2" d="M2964.17,-65.45C2964.17,-61.84 2931.64,-58.91 2891.59,-58.91 2851.53,-58.91 2819,-61.84 2819,-65.45"/>
<text text-anchor="middle" x="2891.59" y="-40.2" font-family="Arial" font-size="14.00">FAQs</text>
<text text-anchor="middle" x="2891.59" y="-23.4" font-family="Arial" font-size="14.00">(Payload::Collection)</text>
</g>
<!-- Admins -->
<g id="node19" class="node">
<title>Admins</title>
<path fill="#fff5cd" stroke="black" stroke-width="2" d="M3127.17,-65.45C3127.17,-69.07 3094.64,-72 3054.59,-72 3014.53,-72 2982,-69.07 2982,-65.45 2982,-65.45 2982,-6.55 2982,-6.55 2982,-2.93 3014.53,0 3054.59,0 3094.64,0 3127.17,-2.93 3127.17,-6.55 3127.17,-6.55 3127.17,-65.45 3127.17,-65.45"/>
<path fill="none" stroke="black" stroke-width="2" d="M3127.17,-65.45C3127.17,-61.84 3094.64,-58.91 3054.59,-58.91 3014.53,-58.91 2982,-61.84 2982,-65.45"/>
<text text-anchor="middle" x="3054.59" y="-40.2" font-family="Arial" font-size="14.00">Admins</text>
<text text-anchor="middle" x="3054.59" y="-23.4" font-family="Arial" font-size="14.00">(Payload::Collection)</text>
</g>
<!-- Emails -->
<g id="node20" class="node">
<title>Emails</title>
<path fill="#fff5cd" stroke="black" stroke-width="2" d="M3290.17,-65.45C3290.17,-69.07 3257.64,-72 3217.59,-72 3177.53,-72 3145,-69.07 3145,-65.45 3145,-65.45 3145,-6.55 3145,-6.55 3145,-2.93 3177.53,0 3217.59,0 3257.64,0 3290.17,-2.93 3290.17,-6.55 3290.17,-6.55 3290.17,-65.45 3290.17,-65.45"/>
<path fill="none" stroke="black" stroke-width="2" d="M3290.17,-65.45C3290.17,-61.84 3257.64,-58.91 3217.59,-58.91 3177.53,-58.91 3145,-61.84 3145,-65.45"/>
<text text-anchor="middle" x="3217.59" y="-40.2" font-family="Arial" font-size="14.00">Emails</text>
<text text-anchor="middle" x="3217.59" y="-23.4" font-family="Arial" font-size="14.00">(Payload::Collection)</text>
</g>
<!-- Logs -->
<g id="node21" class="node">
<title>Logs</title>
<path fill="#fff5cd" stroke="black" stroke-width="2" d="M3453.17,-65.45C3453.17,-69.07 3420.64,-72 3380.59,-72 3340.53,-72 3308,-69.07 3308,-65.45 3308,-65.45 3308,-6.55 3308,-6.55 3308,-2.93 3340.53,0 3380.59,0 3420.64,0 3453.17,-2.93 3453.17,-6.55 3453.17,-6.55 3453.17,-65.45 3453.17,-65.45"/>
<path fill="none" stroke="black" stroke-width="2" d="M3453.17,-65.45C3453.17,-61.84 3420.64,-58.91 3380.59,-58.91 3340.53,-58.91 3308,-61.84 3308,-65.45"/>
<text text-anchor="middle" x="3380.59" y="-40.2" font-family="Arial" font-size="14.00">Logs</text>
<text text-anchor="middle" x="3380.59" y="-23.4" font-family="Arial" font-size="14.00">(Payload::Collection)</text>
</g>
<!-- PostgreSQL Database -->
<g id="node22" class="node">
<title>PostgreSQL Database</title>
<path fill="#ffcfb3" stroke="black" stroke-width="2" d="M3645.94,-65.45C3645.94,-69.07 3606.79,-72 3558.59,-72 3510.39,-72 3471.23,-69.07 3471.23,-65.45 3471.23,-65.45 3471.23,-6.55 3471.23,-6.55 3471.23,-2.93 3510.39,0 3558.59,0 3606.79,0 3645.94,-2.93 3645.94,-6.55 3645.94,-6.55 3645.94,-65.45 3645.94,-65.45"/>
<path fill="none" stroke="black" stroke-width="2" d="M3645.94,-65.45C3645.94,-61.84 3606.79,-58.91 3558.59,-58.91 3510.39,-58.91 3471.23,-61.84 3471.23,-65.45"/>
<text text-anchor="middle" x="3558.59" y="-40.2" font-family="Arial" font-size="14.00">PostgreSQL Database</text>
<text text-anchor="middle" x="3558.59" y="-23.4" font-family="Arial" font-size="14.00">(AWS::RDS::DBInstance)</text>
</g>
<!-- Resend Email Service -->
<g id="node23" class="node">
<title>Resend Email Service</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M3817.66,-72C3817.66,-72 3675.51,-72 3675.51,-72 3669.51,-72 3663.51,-66 3663.51,-60 3663.51,-60 3663.51,-12 3663.51,-12 3663.51,-6 3669.51,0 3675.51,0 3675.51,0 3817.66,0 3817.66,0 3823.66,0 3829.66,-6 3829.66,-12 3829.66,-12 3829.66,-60 3829.66,-60 3829.66,-66 3823.66,-72 3817.66,-72"/>
<text text-anchor="middle" x="3746.59" y="-40.2" font-family="Arial" font-size="14.00">Resend Email Service</text>
<text text-anchor="middle" x="3746.59" y="-23.4" font-family="Arial" font-size="14.00">(External::EmailService)</text>
</g>
<!-- Cron Job -->
<g id="node24" class="node">
<title>Cron Job</title>
<path fill="#e6e6e6" stroke="black" stroke-width="2" d="M3979.77,-72C3979.77,-72 3859.4,-72 3859.4,-72 3853.4,-72 3847.4,-66 3847.4,-60 3847.4,-60 3847.4,-12 3847.4,-12 3847.4,-6 3853.4,0 3859.4,0 3859.4,0 3979.77,0 3979.77,0 3985.77,0 3991.77,-6 3991.77,-12 3991.77,-12 3991.77,-60 3991.77,-60 3991.77,-66 3985.77,-72 3979.77,-72"/>
<text text-anchor="middle" x="3919.59" y="-40.2" font-family="Arial" font-size="14.00">Cron Job</text>
<text text-anchor="middle" x="3919.59" y="-23.4" font-family="Arial" font-size="14.00">(AWS::Events::Rule)</text>
</g>
</g>
</svg>
