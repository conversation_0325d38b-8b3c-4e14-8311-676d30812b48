# Storage Configuration Guide

This guide provides information on how to set up and configure storage options for the OrcheStars application.

## Overview

OrcheStars supports multiple storage providers for media files and exports:

1. **Supabase Storage** - Recommended for both development and production
2. **AWS S3** - Alternative for production environments
3. **Vercel Blob Storage** - Alternative for Vercel deployments
4. **Local File System** - Default fallback for development

The storage configuration automatically selects the appropriate provider based on the environment variables you've set.

## Storage Configuration

The system supports multiple storage providers simultaneously, with the following configuration:

1. **S3 Storage** (including Supabase) - Enabled by default if credentials are provided
2. **Vercel Blob Storage** - Included as a fallback, enabled if S3 is not configured
3. **Default PayloadCMS local storage** - Used if no other storage providers are configured

You can configure both S3 and Vercel Blob storage simultaneously, with S<PERSON> being the primary storage provider if its credentials are available.

## Supabase Storage Configuration

For both development and production environments, we recommend using Supabase Storage through the S3-compatible API:

```
# Supabase S3 Storage Configuration
S3_ACCESS_KEY=supabase
S3_SECRET_KEY=your-supabase-anon-key
S3_BUCKET=your-bucket-name
S3_REGION=us-east-1
S3_ENDPOINT=https://your-project-id.supabase.co/storage/v1
S3_ACL=public-read
```

### Important Notes for Supabase S3 Configuration

1. The `S3_ACCESS_KEY` should be set to `supabase` (this is a placeholder as Supabase uses the anon key for authentication)
2. The `S3_SECRET_KEY` should be your Supabase anon key
3. The `S3_ENDPOINT` must be in the format `https://your-project-id.supabase.co/storage/v1`
4. The `S3_REGION` can be set to `us-east-1` (this is not used by Supabase but required by the S3 client)
5. Make sure your bucket exists in Supabase Storage

### Setting Up Supabase Storage

1. **Local Development**:
   - Start your local Supabase instance using `supabase start`
   - Create a bucket named `media` in the Supabase Studio (http://127.0.0.1:54323)
   - Set the bucket to public if you want files to be publicly accessible
   - Use the anon key from your Supabase project settings

2. **Production**:
   - Create a bucket named `media` in your Supabase project dashboard
   - Set the bucket to public if you want files to be publicly accessible
   - Use the anon key from your Supabase project settings
   - Update your environment variables with the production Supabase URL

### Creating a Bucket in Supabase

To create a bucket in Supabase:

1. Go to the Supabase Studio (local: http://127.0.0.1:54323, production: https://app.supabase.com)
2. Navigate to the Storage section
3. Click "Create bucket"
4. Enter "media" as the bucket name
5. Set the bucket to public or private based on your needs

## AWS S3 Configuration

For alternative production environments, you can use AWS S3 or an S3-compatible service:

```
# AWS S3 Configuration
S3_ACCESS_KEY=your_access_key
S3_SECRET_KEY=your_secret_key
S3_BUCKET=your_bucket_name
S3_REGION=your_region
S3_ENDPOINT=  # Optional: For S3-compatible services
S3_ACL=public-read  # Optional: Default is 'public-read'
```

### S3-Compatible Services

You can use S3-compatible storage services by setting the `S3_ENDPOINT` variable:

| Service | Endpoint Example | Notes |
|---------|-----------------|-------|
| DigitalOcean Spaces | `https://nyc3.digitaloceanspaces.com` | Region is part of the endpoint |
| MinIO | `http://localhost:9000` | For self-hosted MinIO |
| Backblaze B2 | `https://s3.us-west-001.backblazeb2.com` | Requires specific endpoint |
| Wasabi | `https://s3.wasabisys.com` | S3-compatible service |

## Vercel Blob Storage Configuration

If you're deploying on Vercel and prefer to use Vercel Blob Storage:

```
# Vercel Blob Storage
BLOB_READ_WRITE_TOKEN=your_vercel_blob_token
BLOB_BASE_URL=https://your-domain.com
```

To generate a Vercel Blob token:

1. Go to your Vercel project dashboard
2. Navigate to Storage > Blob
3. Create a new token with read/write permissions
4. Add the token to your environment variables

## Local Storage Configuration

For local development, if no storage credentials are provided, the system will default to using the local file system. Files will be stored in the `public/media` directory.

## Storage Implementation Details

The storage configuration is implemented in `src/plugins/storage.ts` and automatically determines which storage provider to use based on the available environment variables.

### Collections Using Storage

The following collections use the configured storage provider:

- `media` - All uploaded media files
- `exports` - Export files generated by the import/export plugin

## Troubleshooting

### Common Issues

1. **Files not appearing in S3 bucket**
   - Verify your S3 credentials are correct
   - Check if the bucket exists and is accessible
   - Ensure the IAM user has appropriate permissions
   - Verify the ACL settings allow public access if needed

2. **CORS issues with S3**
   - Configure CORS on your S3 bucket to allow requests from your domain
   - Example CORS configuration for S3:
     ```json
     [
       {
         "AllowedHeaders": ["*"],
         "AllowedMethods": ["GET", "PUT", "POST", "DELETE", "HEAD"],
         "AllowedOrigins": ["https://your-domain.com"],
         "ExposeHeaders": ["ETag"]
       }
     ]
     ```

3. **Vercel Blob Storage issues**
   - Verify your BLOB_READ_WRITE_TOKEN is correct
   - Check if you've reached your Vercel Blob Storage limits
   - Ensure your Vercel project has Blob Storage enabled

4. **Local storage issues**
   - Check if the `public/media` directory exists and is writable
   - Verify file permissions on the server

## Migrating Between Storage Providers

If you need to migrate files from one storage provider to another:

1. Export your media collection data using the PayloadCMS admin panel
2. Download all media files from the current storage provider
3. Configure the new storage provider
4. Upload the files to the new storage provider
5. Update the media collection data to point to the new file locations
6. Import the updated media collection data

## Additional Resources

- [AWS S3 Documentation](https://docs.aws.amazon.com/s3/)
- [Vercel Blob Storage Documentation](https://vercel.com/docs/storage/vercel-blob)
- [PayloadCMS S3 Plugin Documentation](https://payloadcms.com/docs/plugins/storage-s3)
- [PayloadCMS Vercel Blob Plugin Documentation](https://payloadcms.com/docs/plugins/storage-vercel-blob)
