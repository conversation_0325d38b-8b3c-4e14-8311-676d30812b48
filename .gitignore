build
dist / media
node_modules
.DS_Store
.env
.next
.vercel

# Payload default media upload directory
public/media/

public/robots.txt
public/sitemap*.xml
# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

/.idea/*
!/.idea/runConfigurations

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

.env

/media
tmp

.secrets

# Supabase temporary files
supabase/.temp/
supabase/.bin/
# Sentry Config File
.env.sentry-build-plugin

# testing
/playwright-report/
/test-results/

# vs code
.vscode