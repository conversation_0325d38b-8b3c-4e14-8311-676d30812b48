import { MigrateUpArgs, MigrateDownArgs, sql } from '@payloadcms/db-postgres'

export async function up({ db, payload, req }: MigrateUpArgs): Promise<void> {
  await db.execute(sql`
   ALTER TABLE "orders" ADD COLUMN "expire_at" timestamp(3) with time zone;
  ALTER TABLE "payments" ADD COLUMN "expire_at" timestamp(3) with time zone;`)
}

export async function down({ db, payload, req }: MigrateDownArgs): Promise<void> {
  await db.execute(sql`
   ALTER TABLE "orders" DROP COLUMN IF EXISTS "expire_at";
  ALTER TABLE "payments" DROP COLUMN IF EXISTS "expire_at";`)
}
