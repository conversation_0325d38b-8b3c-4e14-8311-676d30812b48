import { MigrateUpArgs, MigrateDownArgs, sql } from '@payloadcms/db-postgres'

export async function up({ db, payload, req }: MigrateUpArgs): Promise<void> {
  await db.execute(sql`
   ALTER TABLE "checkin_records" RENAME COLUMN "ticket_given_by_id" TO "ticket_given_by";
  ALTER TABLE "checkin_records" DROP CONSTRAINT "checkin_records_ticket_given_by_id_admins_id_fk";
  
  DROP INDEX IF EXISTS "checkin_records_ticket_given_by_idx";`)
}

export async function down({ db, payload, req }: MigrateDownArgs): Promise<void> {
  await db.execute(sql`
   ALTER TABLE "checkin_records" RENAME COLUMN "ticket_given_by" TO "ticket_given_by_id";
  DO $$ BEGIN
   ALTER TABLE "checkin_records" ADD CONSTRAINT "checkin_records_ticket_given_by_id_admins_id_fk" FOREIGN KEY ("ticket_given_by_id") REFERENCES "public"."admins"("id") ON DELETE set null ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  CREATE INDEX IF NOT EXISTS "checkin_records_ticket_given_by_idx" ON "checkin_records" USING btree ("ticket_given_by_id");`)
}
