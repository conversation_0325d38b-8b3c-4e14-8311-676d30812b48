import React from "react";

export default function TermsConditionsAffiliate() {
  return (
    <main className="max-w-2xl mx-auto py-10 px-4">
      <h1 className="text-3xl font-bold mb-6 text-center"><PERSON><PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON><PERSON> kiện dành cho <PERSON> sứ (Affiliate)</h1>
      <section className="mb-6">
        <h2 className="text-xl font-semibold mb-2">1. <PERSON><PERSON><PERSON><PERSON> thiệu</h2>
        <p>
          Chào mừng bạn đến với chương trình Đạ<PERSON> sứ (Affiliate) của chúng tôi. Khi tham gia chương trình này, bạn đồng ý tuân thủ các điều khoản và điều kiện dưới đây.
        </p>
      </section>
      <section className="mb-6">
        <h2 className="text-xl font-semibold mb-2">2. <PERSON><PERSON><PERSON><PERSON> kiện tham gia</h2>
        <ul className="list-disc pl-6">
          <li>Bạn phải từ 18 tuổi trở lên.</li>
          <li>Có tài khoản ngân hàng hợp lệ tại Việt Nam.</li>
          <li>Cam kết cung cấp thông tin chính xác khi đăng ký.</li>
        </ul>
      </section>
      <section className="mb-6">
        <h2 className="text-xl font-semibold mb-2">3. Trách nhiệm của Đại sứ</h2>
        <ul className="list-disc pl-6">
          <li>Quảng bá sản phẩm/dịch vụ một cách trung thực và minh bạch.</li>
          <li>Không được sử dụng thông tin sai lệch hoặc gây hiểu lầm cho khách hàng.</li>
          <li>Tuân thủ các quy định pháp luật hiện hành của Việt Nam.</li>
        </ul>
      </section>
      <section className="mb-6">
        <h2 className="text-xl font-semibold mb-2">4. Hoa hồng & Thanh toán</h2>
        <ul className="list-disc pl-6">
          <li>Hoa hồng sẽ được tính dựa trên số lượng khách hàng hợp lệ mà bạn giới thiệu.</li>
          <li>Thanh toán hoa hồng sẽ được thực hiện vào ngày 10 hàng tháng qua chuyển khoản ngân hàng.</li>
          <li>Bạn chịu trách nhiệm về các khoản thuế liên quan (nếu có).</li>
        </ul>
      </section>
      <section className="mb-6">
        <h2 className="text-xl font-semibold mb-2">5. Hành vi bị cấm</h2>
        <ul className="list-disc pl-6">
          <li>Gian lận, tạo đơn hàng ảo hoặc sử dụng thông tin giả mạo.</li>
          <li>Spam, quảng cáo không phù hợp hoặc gây phiền toái cho người khác.</li>
          <li>Vi phạm quyền sở hữu trí tuệ của bên thứ ba.</li>
        </ul>
      </section>
      <section className="mb-6">
        <h2 className="text-xl font-semibold mb-2">6. Chấm dứt hợp tác</h2>
        <p>
          Chúng tôi có quyền chấm dứt hợp tác với bất kỳ Đại sứ nào vi phạm điều khoản hoặc có hành vi không phù hợp mà không cần báo trước.
        </p>
      </section>
      <section className="mb-6">
        <h2 className="text-xl font-semibold mb-2">7. Liên hệ</h2>
        <p>
          Nếu bạn có bất kỳ câu hỏi nào về chương trình Đại sứ, vui lòng liên hệ với chúng tôi qua email: <a href="mailto:<EMAIL>" className="text-blue-600 underline"><EMAIL></a>.
        </p>
      </section>
    </main>
  );
}
