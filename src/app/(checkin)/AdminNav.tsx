import React from 'react'
import type { Admin } from '@/payload-types'

import { LogoutButton } from './LogoutButton'

type AdminNavProps = {
  admin: Admin | null
}

export const AdminNav: React.FC<AdminNavProps> = ({ admin }) => {
  return (
    <header className="p-4 flex justify-between items-center bg-gray-800 shadow-md">
      <div className="text-sm">
        {admin ? (
          <span>
            Logged in as: <strong>{admin.email}</strong>
          </span>
        ) : (
          <span>Not logged in</span>
        )}
      </div>
      {admin && <LogoutButton />}
    </header>
  )
} 